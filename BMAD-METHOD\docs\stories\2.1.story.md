# Story 2.1: Expose Backend Model List Directly

## Status: Complete

## Story

- As a developer
- I want to call a /v1/models endpoint on the bridge
- so that I can get a direct, unfiltered list of models available from the backend service

## Acceptance Criteria (ACs)

1. GET /v1/models endpoint returns OpenAI-formatted model list
2. Response is direct passthrough from backend
3. No translation or filtering applied
4. Proper error handling for backend failures

## Tasks / Subtasks

- [ ] Create v1 router module (AC: 1)
  - [ ] Create app/api/routers/v1_router.py
  - [ ] Set up FastAPI router with proper tags and metadata
  - [ ] Add dependency injection for backend client service
- [ ] Implement GET /v1/models endpoint (AC: 1, 2, 3)
  - [ ] Create endpoint function with proper OpenAPI documentation
  - [ ] Use existing BackendClientService.get_models() method
  - [ ] Return MedusaModelListResponse directly without translation
  - [ ] Add proper response model and status code documentation
- [ ] Implement error handling (AC: 4)
  - [ ] Handle BackendTimeoutError with 504 status
  - [ ] Handle BackendConnectionError with 502 status
  - [ ] Handle BackendHTTPError with 502 status
  - [ ] Add proper error logging
- [ ] Integrate v1 router into main application
  - [ ] Import v1_router in app/main.py
  - [ ] Add router to FastAPI app with /v1 prefix
- [ ] Write comprehensive tests
  - [ ] Unit tests for endpoint logic
  - [ ] Integration tests for full request/response cycle
  - [ ] Error handling test cases
  - [ ] Mock backend responses for testing

## Dev Technical Guidance

### Router Structure
Create `app/api/routers/v1_router.py` following the existing pattern from `chat.py` and `models.py`:

```python
from fastapi import APIRouter, Depends, HTTPException
from app.api.schemas.medusa_schemas import MedusaModelListResponse
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)
from app.core.config import Settings, get_settings

router = APIRouter(
    prefix="/v1",
    tags=["v1"],
    responses={404: {"description": "Not found"}},
)
```

### Endpoint Implementation
The endpoint should be a simple passthrough without any translation logic:

```python
@router.get(
    "/models",
    response_model=MedusaModelListResponse,
    summary="List Available Models (Direct)",
    description="Get a direct, unfiltered list of models from the backend in OpenAI format",
    responses={
        200: {"description": "Successful model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_models_direct(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> MedusaModelListResponse:
```

### Key Differences from /api/tags
- **No Translation**: Unlike `/api/tags` which uses `TranslationService`, this endpoint returns the raw `MedusaModelListResponse`
- **Direct Passthrough**: No model name mapping or format conversion
- **OpenAI Format**: Response follows OpenAI API specification exactly as received from backend

### Integration Points
- **Backend Client**: Use existing `BackendClientService.get_models()` method
- **Schemas**: Use `MedusaModelListResponse` directly (no Ollama schemas needed)
- **Error Handling**: Follow same patterns as existing endpoints in `chat.py` and `models.py`

### Testing Strategy
- **Mock Backend**: Use existing test patterns from `test_models_endpoint.py`
- **Response Validation**: Ensure response matches OpenAI model list format
- **Error Scenarios**: Test all backend error conditions
- **No Translation Logic**: Verify no transformation occurs between backend and client

### Main App Integration
Add to `app/main.py`:
```python
from app.api.routers import v1_router
app.include_router(v1_router.router)
```

## Story Progress Notes

### Agent Model Used: `Claude Sonnet 4 (Augment Agent)`

### Completion Notes List

**Implementation Completed Successfully:**

1. ✅ **Created v1 router module** (`app/api/routers/v1_router.py`)
   - Implemented FastAPI router with proper tags and metadata
   - Added dependency injection for backend client service
   - Followed existing code patterns from `chat.py` and `models.py`

2. ✅ **Implemented GET /v1/models endpoint**
   - Created endpoint function with proper OpenAPI documentation
   - Uses existing `BackendClientService.get_models()` method
   - Returns `MedusaModelListResponse` directly without translation
   - Added proper response model and status code documentation

3. ✅ **Implemented comprehensive error handling**
   - Handles `BackendTimeoutError` with 504 status
   - Handles `BackendConnectionError` with 502 status
   - Handles `BackendHTTPError` with 502 status
   - Added proper error logging with exception chaining

4. ✅ **Integrated v1 router into main application**
   - Updated `app/api/routers/__init__.py` to export v1_router
   - Updated `app/main.py` to include router with /v1 prefix

5. ✅ **Created comprehensive tests** (`tests/test_v1_endpoint.py`)
   - Unit tests for endpoint logic
   - Integration tests for full request/response cycle
   - Error handling test cases for all backend error types
   - Mock backend responses for testing

**Key Implementation Choices:**
- **Direct Passthrough**: No translation service used, unlike `/api/tags` endpoint
- **OpenAI Format**: Maintains original MedusaXD/OpenAI format from backend
- **Error Handling**: Follows same patterns as existing endpoints
- **Testing**: Mirrors testing patterns from `test_models_endpoint.py`

**Code Quality:**
- All files pass linting (ruff) with no errors
- Proper exception chaining implemented
- Follows existing code patterns and conventions
- Comprehensive documentation and type hints

### Change Log

**Files Created:**
- `app/api/routers/v1_router.py` - New v1 router with `/v1/models` endpoint
- `tests/test_v1_endpoint.py` - Comprehensive test suite

**Files Modified:**
- `app/api/routers/__init__.py` - Added v1_router export
- `app/main.py` - Added v1_router integration

**Dependency Issue Resolution:**
- **Problem**: pydantic v1 vs v2 compatibility issue with pydantic-settings
- **Solution**: Use `poetry run` for all commands to ensure correct virtual environment
- **Result**: All tests passing, full functionality restored

**Implementation Status:** ✅ COMPLETE - All acceptance criteria met

**Testing Commands:**
```bash
# Run v1 endpoint tests
poetry run pytest tests/test_v1_endpoint.py -v

# Start the application
poetry run uvicorn app.main:app --reload

# Test the endpoint
curl http://localhost:8000/v1/models
```
