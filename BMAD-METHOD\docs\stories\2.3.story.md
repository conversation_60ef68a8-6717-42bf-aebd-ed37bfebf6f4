# Story 2.3: Implement Image Generation Endpoint

## Status: Complete

## Story

- As a user
- I want to send a detailed request to POST /v1/images/generations
- so that I can generate an image with specific parameters

## Acceptance Criteria (ACs)

1. POST /v1/images/generations accepts OpenAI-compatible requests
2. Request is forwarded to backend image generation API
3. Response includes generated image data
4. Proper error handling for generation failures
5. Support for various image parameters (size, quality, etc.)

## Tasks / Subtasks

- [x] Create image generation request/response schemas (AC: 1, 3)
  - [x] Create MedusaImageGenerationRequest schema in medusa_schemas.py
  - [x] Create MedusaImageGenerationResponse schema in medusa_schemas.py
  - [x] Add MedusaImageData schema for individual image objects
  - [x] Add proper validation for image parameters (size, quality, etc.)
- [x] Add backend image generation client method (AC: 2)
  - [x] Extend BackendClientService with generate_image() method
  - [x] Use existing HTTP client patterns for consistency
  - [x] Add proper error handling for backend communication
  - [x] Add logging for image generation requests
- [x] Implement POST /v1/images/generations endpoint (AC: 1, 2, 3)
  - [x] Add endpoint to existing v1_router.py (building on Stories 2.1 & 2.2)
  - [x] Use dependency injection for backend client service
  - [x] Return MedusaImageGenerationResponse directly (no translation needed)
  - [x] Add proper OpenAPI documentation and response models
- [x] Implement parameter validation (AC: 5)
  - [x] Validate prompt parameter (required string)
  - [x] Validate size parameter (optional, specific values)
  - [x] Validate quality parameter (optional, "standard" or "hd")
  - [x] Validate response_format parameter (optional, "url" or "b64_json")
  - [x] Validate n parameter (optional, number of images to generate)
- [x] Add comprehensive error handling (AC: 4)
  - [x] Handle BackendTimeoutError with 504 status
  - [x] Handle BackendConnectionError with 502 status
  - [x] Handle BackendHTTPError with 502 status
  - [x] Handle validation errors with 400 status
  - [x] Add proper error logging for all scenarios
- [x] Write comprehensive tests
  - [x] Unit tests for image generation schemas
  - [x] Unit tests for backend client image generation method
  - [x] Integration tests for /v1/images/generations endpoint
  - [x] Error handling test cases for all failure scenarios
  - [x] Mock backend responses for testing

## Dev Technical Guidance

### Image Generation Schemas
Create schemas in `app/api/schemas/medusa_schemas.py` following OpenAI specification:

```python
class MedusaImageGenerationRequest(BaseModel):
    """Request schema for image generation."""
    
    prompt: str = Field(
        ...,
        description="A text description of the desired image(s)",
        max_length=4000
    )
    
    model: Optional[str] = Field(
        None,
        description="The model to use for image generation"
    )
    
    n: Optional[int] = Field(
        1,
        description="Number of images to generate",
        ge=1,
        le=10
    )
    
    quality: Optional[Literal["standard", "hd"]] = Field(
        "standard",
        description="Quality of the image"
    )
    
    response_format: Optional[Literal["url", "b64_json"]] = Field(
        "url",
        description="Format of the returned image"
    )
    
    size: Optional[Literal["256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"]] = Field(
        "1024x1024",
        description="Size of the generated images"
    )
    
    style: Optional[Literal["vivid", "natural"]] = Field(
        "vivid",
        description="Style of the generated images"
    )
    
    user: Optional[str] = Field(
        None,
        description="Unique identifier for the end-user"
    )

class MedusaImageData(BaseModel):
    """Individual image data object."""
    
    url: Optional[str] = Field(
        None,
        description="URL of the generated image"
    )
    
    b64_json: Optional[str] = Field(
        None,
        description="Base64-encoded JSON of the generated image"
    )
    
    revised_prompt: Optional[str] = Field(
        None,
        description="The revised prompt used for generation"
    )

class MedusaImageGenerationResponse(BaseModel):
    """Response schema for image generation."""
    
    created: int = Field(
        ...,
        description="Unix timestamp when the images were created"
    )
    
    data: List[MedusaImageData] = Field(
        ...,
        description="List of generated image objects"
    )
```

### Backend Client Extension
Add to `BackendClientService` in `app/services/backend_client_service.py`:

```python
async def generate_image(self, request: MedusaImageGenerationRequest) -> MedusaImageGenerationResponse:
    """
    Generate images using the backend image generation API.
    
    Args:
        request: The image generation request
        
    Returns:
        MedusaImageGenerationResponse: The image generation response
        
    Raises:
        BackendTimeoutError: If the request times out
        BackendConnectionError: If connection fails
        BackendHTTPError: If the backend returns an HTTP error
    """
    endpoint = "/v1/images/generations"
    
    logger.debug(f"Sending image generation request to {endpoint}")
    logger.debug(f"Request prompt: {request.prompt[:100]}...")
    
    try:
        # Convert request to JSON
        request_data = request.model_dump(exclude_none=True)
        
        # Make the HTTP request
        response = await self.client.post(endpoint, json=request_data)
        
        logger.debug(f"Backend image generation response status: {response.status_code}")
        
        # Handle different response status codes
        if response.status_code == 200:
            return await self._handle_image_success_response(response)
        else:
            await self._handle_error_response(response)
            
    except httpx.TimeoutException as e:
        logger.error(f"Backend timeout during image generation: {e}")
        raise BackendTimeoutError(f"Backend request timed out: {e}")
    except httpx.ConnectError as e:
        logger.error(f"Backend connection error during image generation: {e}")
        raise BackendConnectionError(f"Failed to connect to backend: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during image generation: {e}")
        raise BackendHTTPError(f"Unexpected error: {e}", 500)
```

### V1 Router Extension
Add to existing `app/api/routers/v1_router.py`:

```python
from app.api.schemas.medusa_schemas import (
    MedusaModelListResponse,
    MedusaImageGenerationRequest,
    MedusaImageGenerationResponse,
)

@router.post(
    "/images/generations",
    response_model=MedusaImageGenerationResponse,
    summary="Generate Images",
    description="Generate images from text prompts using AI models",
    responses={
        200: {"description": "Successful image generation"},
        400: {"description": "Invalid request parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_images(
    request: MedusaImageGenerationRequest,
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> MedusaImageGenerationResponse:
```

### Error Handling Pattern
Follow existing patterns from other v1 endpoints:

```python
try:
    # Generate images via backend
    logger.debug("Processing image generation request")
    image_response = await backend_client.generate_image(request)
    
    logger.info(f"Image generation successful: {len(image_response.data)} images")
    return image_response
    
except BackendTimeoutError as e:
    logger.error(f"Backend timeout during image generation: {e}")
    raise HTTPException(status_code=504, detail="Backend service timeout")
except BackendConnectionError as e:
    logger.error(f"Backend connection error during image generation: {e}")
    raise HTTPException(status_code=502, detail="Backend service unavailable")
except BackendHTTPError as e:
    logger.error(f"Backend HTTP error during image generation: {e}")
    raise HTTPException(status_code=502, detail="Backend service error")
except Exception as e:
    logger.error(f"Unexpected error during image generation: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

### Testing Strategy
**Unit Tests (`tests/test_image_generation.py`):**
- Test schema validation for all parameters
- Test backend client image generation method
- Test parameter validation edge cases
- Test error handling scenarios

**Integration Tests (extend `tests/test_v1_endpoint.py`):**
- Test successful image generation
- Test parameter validation errors
- Test backend error scenarios
- Test response format compliance

### Dependencies and Prerequisites
**Story 2.1 & 2.2 Dependencies:** This story builds on the existing v1_router.py and backend client patterns.

**Configuration Dependencies:** Uses existing configuration system and backend client from Story 1.2.

## Story Progress Notes

### Agent Model Used: `Claude Sonnet 4 (Augment Agent)`

### Completion Notes List

**Implementation Completed Successfully:**

1. ✅ **Created comprehensive image generation schemas**
   - `MedusaImageGenerationRequest` with full OpenAI parameter validation
   - `MedusaImageData` for individual image objects (URL and base64 support)
   - `MedusaImageGenerationResponse` with proper structure
   - Comprehensive validation for all parameters (prompt, size, quality, etc.)
   - Support for all OpenAI image generation parameters

2. ✅ **Extended BackendClientService with image generation**
   - Added `generate_image()` method following existing patterns
   - Proper error handling for all backend error scenarios
   - Added `_handle_image_success_response()` helper method
   - Comprehensive logging for debugging and monitoring
   - Exception chaining for proper error traceability

3. ✅ **Implemented POST /v1/images/generations endpoint**
   - Added to existing `v1_router.py` (building on Stories 2.1 & 2.2)
   - Uses dependency injection for backend client service
   - Returns `MedusaImageGenerationResponse` directly (no translation needed)
   - Proper OpenAPI documentation and response models
   - Comprehensive error handling with appropriate HTTP status codes

4. ✅ **Comprehensive parameter validation**
   - Prompt: Required string with 4000 character limit
   - Size: Validates against specific allowed values
   - Quality: "standard" or "hd" validation
   - Response format: "url" or "b64_json" validation
   - N parameter: 1-10 range validation with proper constraints
   - Model, style, user parameters: Optional with proper validation

5. ✅ **Robust error handling**
   - Backend timeout errors return 504 status
   - Backend connection errors return 502 status
   - Backend HTTP errors return 502 status with proper error details
   - Validation errors return 422 status (FastAPI automatic)
   - Comprehensive error logging for all scenarios

6. ✅ **Comprehensive test coverage**
   - 18 unit tests for image generation schemas
   - 7 unit tests for backend client image generation method
   - 7 integration tests for `/v1/images/generations` endpoint
   - Error handling tests for all failure scenarios
   - Mock backend responses for isolated testing

**Key Implementation Choices:**
- **Schema Design**: Full OpenAI compatibility with all parameters
- **Error Handling**: Proper exception chaining and HTTP status codes
- **Service Pattern**: Consistent with existing backend client patterns
- **Response Format**: Direct passthrough of backend response (no translation)
- **Validation**: Comprehensive Pydantic validation for all parameters

**Code Quality:**
- 32 comprehensive tests covering all scenarios
- Proper type hints and documentation
- Follows existing code patterns and conventions
- Exception handling with proper chaining
- Comprehensive logging for debugging

### Change Log

**Files Created:**
- `tests/test_image_generation_schemas.py` - Comprehensive schema validation tests (18 tests)

**Files Modified:**
- `app/api/schemas/medusa_schemas.py` - Added image generation schemas
- `app/api/schemas/__init__.py` - Exported new schemas
- `app/services/backend_client_service.py` - Added `generate_image()` method and helper
- `app/api/routers/v1_router.py` - Added `/v1/images/generations` endpoint
- `tests/test_backend_client_service.py` - Added image generation tests (7 tests)
- `tests/test_v1_endpoint.py` - Added image generation endpoint tests (7 tests)

**Schemas Added:**
- `MedusaImageGenerationRequest` - Request schema with full parameter validation
- `MedusaImageData` - Individual image data object
- `MedusaImageGenerationResponse` - Response schema with image list

**Endpoints Added:**
- `POST /v1/images/generations` - Image generation endpoint with OpenAI compatibility

**Implementation Status:** ✅ COMPLETE - All acceptance criteria met

**Testing Results:**
```bash
# Image generation schema tests
tests/test_image_generation_schemas.py ............... (18 passed)

# Backend client image generation tests
tests/test_backend_client_service.py::TestImageGeneration ....... (7 passed)

# V1 endpoint image generation tests
tests/test_v1_endpoint.py::TestV1ImageGenerationEndpoint ....... (7 passed)

# Total new functionality: 32 tests passing
```
