"""
V1 router for OpenAI-compatible endpoints.

This module provides endpoints that return data in OpenAI format directly
from the backend without translation to Ollama format.
"""

import logging

from fastapi import APIRouter, Depends, HTTPException

from app.api.schemas.medusa_schemas import (
    MedusaImageGenerationRequest,
    MedusaImageGenerationResponse,
    MedusaModelListResponse,
)
from app.core.config import Settings, get_settings
from app.services.backend_client_service import (
    BackendClientService,
    BackendConnectionError,
    BackendHTTPError,
    BackendTimeoutError,
    get_backend_client,
)
from app.services.model_filter_service import (
    ModelFilterService,
    get_model_filter_service,
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/v1",
    tags=["v1"],
    responses={404: {"description": "Not found"}},
)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


async def get_model_filter_service_dependency(settings: Settings = Depends(get_settings)) -> ModelFilterService:
    """
    Dependency to get the model filter service.

    Args:
        settings: Application settings

    Returns:
        ModelFilterService: Configured model filter service
    """
    return get_model_filter_service(settings)


@router.get(
    "/models",
    response_model=MedusaModelListResponse,
    summary="List Available Models (Direct)",
    description="Get a direct, unfiltered list of models from the backend in OpenAI format",
    responses={
        200: {"description": "Successful model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_models_direct(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> MedusaModelListResponse:
    """
    List available models in OpenAI format (direct passthrough).

    This endpoint retrieves the model list from the backend API and returns
    it directly without any translation or filtering. The response follows
    the OpenAI API specification exactly as received from the backend.

    Args:
        backend_client: Client for backend API communication

    Returns:
        MedusaModelListResponse: The model list in OpenAI format

    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.debug("Direct model list request received")

    try:
        # Get model list from backend (direct passthrough)
        logger.debug("Requesting model list from backend")
        medusa_response = await backend_client.get_models()

        logger.info(f"Direct model list retrieved successfully: {len(medusa_response.data)} models")
        return medusa_response

    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during direct model list request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving model list"
        ) from e

    except BackendConnectionError as e:
        logger.error(f"Backend connection error during direct model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        ) from e

    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during direct model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error while retrieving model list"
        ) from e

    except Exception as e:
        logger.error(f"Unexpected error during direct model list request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while retrieving model list"
        ) from e


@router.get(
    "/TTI/models",
    response_model=MedusaModelListResponse,
    summary="List Text-to-Image Models",
    description="Get filtered list of text-to-image models based on configured patterns and keywords",
    responses={
        200: {"description": "Successful TTI model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_tti_models(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
    filter_service: ModelFilterService = Depends(get_model_filter_service_dependency),
) -> MedusaModelListResponse:
    """
    List available text-to-image models.

    This endpoint retrieves the model list from the backend API and filters
    it to return only text-to-image models based on configured patterns and
    keywords. The response follows the OpenAI API specification.

    Args:
        backend_client: Client for backend API communication
        filter_service: Service for filtering TTI models

    Returns:
        MedusaModelListResponse: The filtered TTI model list in OpenAI format

    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.debug("TTI model list request received")

    try:
        # Get all models from backend
        logger.debug("Requesting model list from backend for TTI filtering")
        all_models_response = await backend_client.get_models()

        # Filter for TTI models
        logger.debug(f"Filtering {len(all_models_response.data)} models for TTI models")
        filtered_models = filter_service.filter_tti_models(all_models_response.data)

        # Return filtered response
        filtered_response = MedusaModelListResponse(
            object="list",
            data=filtered_models
        )

        logger.info(f"TTI model list retrieved successfully: {len(filtered_models)} models")
        return filtered_response

    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during TTI model list request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving TTI model list"
        ) from e

    except BackendConnectionError as e:
        logger.error(f"Backend connection error during TTI model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        ) from e

    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during TTI model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error while retrieving TTI model list"
        ) from e

    except Exception as e:
        logger.error(f"Unexpected error during TTI model filtering: {e}")
        logger.warning("Falling back to returning all models due to filtering error")
        # Fallback: return all models if filtering fails
        try:
            all_models_response = await backend_client.get_models()
            logger.info(f"Fallback: returning all {len(all_models_response.data)} models")
            return all_models_response
        except Exception as fallback_error:
            logger.error(f"Fallback also failed: {fallback_error}")
            raise HTTPException(
                status_code=500,
                detail="Internal server error while retrieving TTI model list"
            ) from e


@router.post(
    "/images/generations",
    response_model=MedusaImageGenerationResponse,
    summary="Generate Images",
    description="Generate images from text prompts using AI models",
    responses={
        200: {"description": "Successful image generation"},
        400: {"description": "Invalid request parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_images(
    request: MedusaImageGenerationRequest,
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> MedusaImageGenerationResponse:
    """
    Generate images from text prompts.

    This endpoint accepts image generation requests in OpenAI format and forwards
    them to the backend image generation API. The response includes generated
    image data in the requested format.

    Args:
        request: The image generation request with prompt and parameters
        backend_client: Client for backend API communication

    Returns:
        MedusaImageGenerationResponse: The image generation response with image data

    Raises:
        HTTPException: For various error conditions (400, 502, 504)
    """
    logger.debug("Image generation request received")
    logger.debug(f"Request prompt: {request.prompt[:100]}...")

    try:
        # Generate images via backend
        logger.debug("Processing image generation request")
        image_response = await backend_client.generate_image(request)

        logger.info(f"Image generation successful: {len(image_response.data)} images")
        return image_response

    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during image generation: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while generating images"
        ) from e

    except BackendConnectionError as e:
        logger.error(f"Backend connection error during image generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        ) from e

    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during image generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error while generating images"
        ) from e

    except Exception as e:
        logger.error(f"Unexpected error during image generation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while generating images"
        ) from e
