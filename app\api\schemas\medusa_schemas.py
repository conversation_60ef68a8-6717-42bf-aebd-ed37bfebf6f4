"""
MedusaXD/OpenAI API format schemas.

This module contains Pydantic models that represent the OpenAI-compatible API format
used by the MedusaXD backend service.
"""

from typing import Any, Literal, Optional, Union

from pydantic import BaseModel, Field


class MedusaMessage(BaseModel):
    """
    Represents a message in OpenAI chat format.

    Supports various roles and content types including multimodal content.
    """

    role: Literal["system", "user", "assistant", "tool", "function"] = Field(
        ...,
        description="The role of the message sender"
    )

    content: Union[str, list[dict[str, Any]], None] = Field(
        ...,
        description="The message content (string or multimodal parts)"
    )

    name: Optional[str] = Field(
        None,
        description="Optional name for the message sender"
    )

    tool_calls: Optional[list[dict[str, Any]]] = Field(
        None,
        description="Tool calls made by the assistant"
    )

    tool_call_id: Optional[str] = Field(
        None,
        description="ID of the tool call being responded to"
    )


class MedusaChatRequest(BaseModel):
    """
    Represents an OpenAI-compatible chat completion request.

    This matches the format expected by the MedusaXD backend's
    /v1/chat/completions endpoint.
    """

    model: str = Field(
        ...,
        description="The model to use for the chat completion"
    )

    messages: list[MedusaMessage] = Field(
        ...,
        description="List of messages in the conversation"
    )

    stream: Optional[bool] = Field(
        False,
        description="Whether to stream the response"
    )

    temperature: Optional[float] = Field(
        None,
        description="Sampling temperature (0-2)",
        ge=0.0,
        le=2.0
    )

    max_tokens: Optional[int] = Field(
        None,
        description="Maximum number of tokens to generate",
        gt=0
    )

    top_p: Optional[float] = Field(
        None,
        description="Nucleus sampling parameter",
        ge=0.0,
        le=1.0
    )

    frequency_penalty: Optional[float] = Field(
        None,
        description="Frequency penalty (-2.0 to 2.0)",
        ge=-2.0,
        le=2.0
    )

    presence_penalty: Optional[float] = Field(
        None,
        description="Presence penalty (-2.0 to 2.0)",
        ge=-2.0,
        le=2.0
    )

    stop: Optional[Union[str, list[str]]] = Field(
        None,
        description="Stop sequences"
    )

    n: Optional[int] = Field(
        None,
        description="Number of completions to generate",
        gt=0
    )

    user: Optional[str] = Field(
        None,
        description="User identifier for tracking"
    )


class MedusaUsage(BaseModel):
    """
    Represents token usage information in the response.
    """

    prompt_tokens: int = Field(
        ...,
        description="Number of tokens in the prompt"
    )

    completion_tokens: int = Field(
        ...,
        description="Number of tokens in the completion"
    )

    total_tokens: int = Field(
        ...,
        description="Total number of tokens used"
    )


class MedusaChoice(BaseModel):
    """
    Represents a single choice in the chat completion response.
    """

    index: int = Field(
        ...,
        description="Index of this choice"
    )

    message: MedusaMessage = Field(
        ...,
        description="The generated message"
    )

    finish_reason: Optional[Literal["stop", "length", "content_filter", "tool_calls", "function_call"]] = Field(
        None,
        description="Reason why the generation stopped"
    )

    logprobs: Optional[dict[str, Any]] = Field(
        None,
        description="Log probabilities for tokens"
    )


class MedusaChatResponse(BaseModel):
    """
    Represents an OpenAI-compatible chat completion response.

    This matches the format returned by the MedusaXD backend's
    /v1/chat/completions endpoint.
    """

    id: str = Field(
        ...,
        description="Unique identifier for the completion"
    )

    object: Literal["chat.completion"] = Field(
        "chat.completion",
        description="Object type"
    )

    created: int = Field(
        ...,
        description="Unix timestamp when the completion was created"
    )

    model: str = Field(
        ...,
        description="The model used for the completion"
    )

    choices: list[MedusaChoice] = Field(
        ...,
        description="List of completion choices"
    )

    usage: Optional[MedusaUsage] = Field(
        None,
        description="Token usage information"
    )

    system_fingerprint: Optional[str] = Field(
        None,
        description="System fingerprint"
    )


class MedusaStreamChoice(BaseModel):
    """
    Represents a choice in a streaming response chunk.
    """

    index: int = Field(
        ...,
        description="Index of this choice"
    )

    delta: dict[str, Any] = Field(
        ...,
        description="Delta containing the new content"
    )

    finish_reason: Optional[str] = Field(
        None,
        description="Reason why the generation stopped"
    )


class MedusaStreamResponse(BaseModel):
    """
    Represents a single chunk in an OpenAI-compatible streaming response.
    """

    id: str = Field(
        ...,
        description="Unique identifier for the completion"
    )

    object: Literal["chat.completion.chunk"] = Field(
        "chat.completion.chunk",
        description="Object type"
    )

    created: int = Field(
        ...,
        description="Unix timestamp when the chunk was created"
    )

    model: str = Field(
        ...,
        description="The model generating the response"
    )

    choices: list[MedusaStreamChoice] = Field(
        ...,
        description="List of choice deltas"
    )

    system_fingerprint: Optional[str] = Field(
        None,
        description="System fingerprint"
    )


class MedusaErrorResponse(BaseModel):
    """
    Represents an error response from the MedusaXD backend.
    """

    error: dict[str, Any] = Field(
        ...,
        description="Error details"
    )


class MedusaErrorDetail(BaseModel):
    """
    Represents detailed error information.
    """

    message: str = Field(
        ...,
        description="Error message"
    )

    type: str = Field(
        ...,
        description="Error type"
    )

    param: Optional[str] = Field(
        None,
        description="Parameter that caused the error"
    )

    code: Optional[str] = Field(
        None,
        description="Error code"
    )


class MedusaModel(BaseModel):
    """
    Represents a model in the MedusaXD/OpenAI model list format.

    This matches the format returned by the MedusaXD backend's
    /v1/models endpoint for individual model objects.
    """

    id: str = Field(
        ...,
        description="Unique identifier for the model"
    )

    object: Literal["model"] = Field(
        "model",
        description="Object type"
    )

    created: int = Field(
        ...,
        description="Unix timestamp when the model was created"
    )

    owned_by: str = Field(
        ...,
        description="Organization that owns the model"
    )


class MedusaModelListResponse(BaseModel):
    """
    Represents a model list response from the MedusaXD backend.

    This matches the format returned by the MedusaXD backend's
    /v1/models endpoint.
    """

    object: Literal["list"] = Field(
        "list",
        description="Object type"
    )

    data: list[MedusaModel] = Field(
        ...,
        description="List of available models"
    )


class MedusaImageGenerationRequest(BaseModel):
    """
    Represents an image generation request to the MedusaXD backend.

    This matches the format expected by the MedusaXD backend's
    /v1/images/generations endpoint.
    """

    prompt: str = Field(
        ...,
        description="A text description of the desired image(s)",
        max_length=4000
    )

    model: Optional[str] = Field(
        None,
        description="The model to use for image generation"
    )

    n: Optional[int] = Field(
        1,
        description="Number of images to generate",
        ge=1,
        le=10
    )

    quality: Optional[Literal["standard", "hd"]] = Field(
        "standard",
        description="Quality of the image"
    )

    response_format: Optional[Literal["url", "b64_json"]] = Field(
        "url",
        description="Format of the returned image"
    )

    size: Optional[Literal["256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"]] = Field(
        "1024x1024",
        description="Size of the generated images"
    )

    style: Optional[Literal["vivid", "natural"]] = Field(
        "vivid",
        description="Style of the generated images"
    )

    user: Optional[str] = Field(
        None,
        description="Unique identifier for the end-user"
    )


class MedusaImageData(BaseModel):
    """
    Represents individual image data in the response.

    Contains either a URL or base64-encoded image data depending on
    the response_format parameter in the request.
    """

    url: Optional[str] = Field(
        None,
        description="URL of the generated image"
    )

    b64_json: Optional[str] = Field(
        None,
        description="Base64-encoded JSON of the generated image"
    )

    revised_prompt: Optional[str] = Field(
        None,
        description="The revised prompt used for generation"
    )


class MedusaImageGenerationResponse(BaseModel):
    """
    Represents an image generation response from the MedusaXD backend.

    This matches the format returned by the MedusaXD backend's
    /v1/images/generations endpoint.
    """

    created: int = Field(
        ...,
        description="Unix timestamp when the images were created"
    )

    data: list[MedusaImageData] = Field(
        ...,
        description="List of generated image objects"
    )
