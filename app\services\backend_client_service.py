"""
Backend client service for communicating with the MedusaXD API.

This module provides an HTTP client for making requests to the backend API
with proper error handling, timeouts, and logging.
"""

import json
import logging
from collections.abc import AsyncGenerator
from typing import Any, Optional

import httpx

from app.api.schemas.medusa_schemas import (
    MedusaChatRequest,
    MedusaChatResponse,
    MedusaErrorResponse,
    MedusaImageGenerationRequest,
    MedusaImageGenerationResponse,
    MedusaModelListResponse,
    MedusaStreamResponse,
)
from app.core.config import Settings

logger = logging.getLogger(__name__)


class BackendClientError(Exception):
    """Base exception for backend client errors."""
    pass


class BackendTimeoutError(BackendClientError):
    """Exception raised when backend request times out."""
    pass


class BackendConnectionError(BackendClientError):
    """Exception raised when backend connection fails."""
    pass


class BackendHTTPError(BackendClientError):
    """Exception raised for HTTP errors from backend."""

    def __init__(self, message: str, status_code: int, response_data: Optional[dict[str, Any]] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class BackendClientService:
    """
    HTTP client service for communicating with the MedusaXD backend API.

    Handles request/response processing, error handling, and logging.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the backend client service.

        Args:
            settings: Application settings containing backend configuration
        """
        self.settings = settings
        self.base_url = settings.medusa_backend_url.rstrip('/')
        self.timeout = settings.api_timeout

        # Create HTTP client with configuration
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(self.timeout),
            limits=httpx.Limits(
                max_connections=settings.max_concurrent_requests,
                max_keepalive_connections=20
            ),
            headers={
                "Content-Type": "application/json",
                "User-Agent": f"{settings.app_name}/0.1.0"
            }
        )

        logger.info(f"Backend client initialized for {self.base_url}")

    async def chat_completion(self, request: MedusaChatRequest) -> MedusaChatResponse:
        """
        Send a non-streaming chat completion request to the backend.

        Args:
            request: The chat completion request (with stream=False)

        Returns:
            MedusaChatResponse: The chat completion response

        Raises:
            BackendTimeoutError: If the request times out
            BackendConnectionError: If connection fails
            BackendHTTPError: If the backend returns an HTTP error
        """
        if request.stream:
            raise ValueError("Use chat_completion_stream() for streaming requests")

        endpoint = "/v1/chat/completions"

        logger.debug(f"Sending non-streaming chat completion request to {endpoint}")
        logger.debug(f"Request model: {request.model}, messages: {len(request.messages)}")

        try:
            # Convert request to JSON
            request_data = request.model_dump(exclude_none=True)

            # Make the HTTP request
            response = await self.client.post(
                endpoint,
                json=request_data
            )

            logger.debug(f"Backend response status: {response.status_code}")

            # Handle different response status codes
            if response.status_code == 200:
                return await self._handle_success_response(response)
            else:
                await self._handle_error_response(response)

        except httpx.TimeoutException as e:
            logger.error(f"Backend request timeout after {self.timeout}s: {e}")
            raise BackendTimeoutError(f"Backend request timed out after {self.timeout} seconds")

        except httpx.ConnectError as e:
            logger.error(f"Backend connection error: {e}")
            raise BackendConnectionError(f"Failed to connect to backend: {e}")

        except httpx.RequestError as e:
            logger.error(f"Backend request error: {e}")
            raise BackendConnectionError(f"Request error: {e}")

    async def chat_completion_stream(self, request: MedusaChatRequest) -> AsyncGenerator[MedusaStreamResponse, None]:
        """
        Send a streaming chat completion request to the backend.

        Args:
            request: The chat completion request (with stream=True)

        Yields:
            MedusaStreamResponse: Individual response chunks

        Raises:
            BackendTimeoutError: If the request times out
            BackendConnectionError: If connection fails
            BackendHTTPError: If the backend returns an HTTP error
        """
        if not request.stream:
            raise ValueError("Use chat_completion() for non-streaming requests")

        endpoint = "/v1/chat/completions"

        logger.debug(f"Sending streaming chat completion request to {endpoint}")
        logger.debug(f"Request model: {request.model}, messages: {len(request.messages)}")

        try:
            # Convert request to JSON
            request_data = request.model_dump(exclude_none=True)

            # Make the streaming HTTP request
            async with self.client.stream(
                "POST",
                endpoint,
                json=request_data
            ) as response:

                logger.debug(f"Backend streaming response status: {response.status_code}")

                # Handle error status codes
                if response.status_code != 200:
                    error_content = await response.aread()
                    await self._handle_streaming_error_response(response, error_content)

                # Process streaming response
                async for chunk in self._process_streaming_response(response):
                    yield chunk

        except BackendHTTPError:
            # Re-raise BackendHTTPError as-is (from _handle_streaming_error_response)
            raise
        except httpx.TimeoutException as e:
            logger.error(f"Backend streaming request timeout: {e}")
            raise BackendTimeoutError(f"Backend streaming request timed out after {self.timeout}s")
        except httpx.ConnectError as e:
            logger.error(f"Backend streaming connection error: {e}")
            raise BackendConnectionError(f"Failed to connect to backend for streaming: {str(e)}")
        except httpx.HTTPStatusError as e:
            logger.error(f"Backend streaming HTTP error: {e}")
            raise BackendHTTPError(f"Backend streaming HTTP error: {e.response.status_code}", e.response.status_code)
        except Exception as e:
            logger.error(f"Unexpected error during streaming chat completion: {e}")
            raise BackendConnectionError(f"Unexpected streaming error: {str(e)}")

    async def _handle_success_response(self, response: httpx.Response) -> MedusaChatResponse:
        """
        Handle a successful response from the backend.

        Args:
            response: The HTTP response

        Returns:
            MedusaChatResponse: The parsed response
        """
        try:
            response_data = response.json()
            logger.debug(f"Backend response data keys: {list(response_data.keys())}")

            # Parse and validate the response
            medusa_response = MedusaChatResponse(**response_data)

            logger.info(f"Chat completion successful: {len(medusa_response.choices)} choices")
            return medusa_response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse backend response JSON: {e}")
            raise BackendHTTPError(
                "Backend returned invalid JSON response",
                response.status_code,
                {"raw_response": response.text}
            )

    async def _handle_models_success_response(self, response: httpx.Response) -> MedusaModelListResponse:
        """
        Handle a successful model list response from the backend.

        Args:
            response: The HTTP response

        Returns:
            MedusaModelListResponse: The parsed response
        """
        try:
            response_data = response.json()
            logger.debug(f"Backend model list response data keys: {list(response_data.keys())}")

            # Parse and validate the response
            model_list_response = MedusaModelListResponse(**response_data)

            logger.info(f"Model list retrieved successfully: {len(model_list_response.data)} models")
            return model_list_response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse backend model list response JSON: {e}")
            raise BackendHTTPError(
                "Backend returned invalid JSON response",
                response.status_code,
                {"raw_response": response.text}
            )
        except Exception as e:
            logger.error(f"Failed to validate backend model list response: {e}")
            raise BackendHTTPError(
                f"Backend returned invalid model list format: {e}",
                response.status_code,
                {"raw_response": response.text}
            )

    async def _handle_image_success_response(self, response: httpx.Response) -> MedusaImageGenerationResponse:
        """
        Handle a successful image generation response from the backend.

        Args:
            response: The HTTP response

        Returns:
            MedusaImageGenerationResponse: The parsed response
        """
        try:
            response_data = response.json()
            logger.debug(f"Backend image generation response data keys: {list(response_data.keys())}")

            # Parse and validate the response
            image_response = MedusaImageGenerationResponse(**response_data)

            logger.info(f"Image generation successful: {len(image_response.data)} images")
            return image_response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse backend image generation response JSON: {e}")
            raise BackendHTTPError(
                "Backend returned invalid JSON response",
                response.status_code,
                {"raw_response": response.text}
            ) from e
        except Exception as e:
            logger.error(f"Failed to validate backend image generation response: {e}")
            raise BackendHTTPError(
                f"Backend returned invalid image generation format: {e}",
                response.status_code,
                {"raw_response": response.text}
            ) from e

    async def _handle_error_response(self, response: httpx.Response) -> None:
        """
        Handle an error response from the backend.

        Args:
            response: The HTTP response

        Raises:
            BackendHTTPError: Always raises with appropriate error details
        """
        try:
            error_data = response.json()
            logger.warning(f"Backend error response: {error_data}")

            # Try to parse as MedusaErrorResponse
            try:
                error_response = MedusaErrorResponse(**error_data)
                error_message = error_response.error.get("message", "Unknown backend error")
            except Exception:
                # Fallback to raw error data
                error_message = str(error_data)

        except json.JSONDecodeError:
            error_message = f"Backend returned {response.status_code}: {response.text}"
            error_data = {"raw_response": response.text}

        logger.error(f"Backend HTTP error {response.status_code}: {error_message}")

        raise BackendHTTPError(
            error_message,
            response.status_code,
            error_data
        )

    async def get_models(self) -> MedusaModelListResponse:
        """
        Get the list of available models from the backend.

        Returns:
            MedusaModelListResponse: The model list response

        Raises:
            BackendTimeoutError: If the request times out
            BackendConnectionError: If connection fails
            BackendHTTPError: If the backend returns an HTTP error
        """
        endpoint = "/v1/models"

        logger.debug(f"Requesting model list from {endpoint}")

        try:
            # Make the HTTP request
            response = await self.client.get(endpoint)

            logger.debug(f"Backend model list response status: {response.status_code}")

            # Handle different response status codes
            if response.status_code == 200:
                return await self._handle_models_success_response(response)
            else:
                await self._handle_error_response(response)

        except httpx.TimeoutException as e:
            logger.error(f"Backend model list request timed out: {e}")
            raise BackendTimeoutError(f"Model list request timed out after {self.timeout}s")

        except httpx.RequestError as e:
            logger.error(f"Backend model list request failed: {e}")
            raise BackendConnectionError(f"Model list request failed: {e}")

    async def generate_image(self, request: MedusaImageGenerationRequest) -> MedusaImageGenerationResponse:
        """
        Generate images using the backend image generation API.

        Args:
            request: The image generation request

        Returns:
            MedusaImageGenerationResponse: The image generation response

        Raises:
            BackendTimeoutError: If the request times out
            BackendConnectionError: If connection fails
            BackendHTTPError: If the backend returns an HTTP error
        """
        endpoint = "/v1/images/generations"

        logger.debug(f"Sending image generation request to {endpoint}")
        logger.debug(f"Request prompt: {request.prompt[:100]}...")

        try:
            # Convert request to JSON
            request_data = request.model_dump(exclude_none=True)

            # Make the HTTP request
            response = await self.client.post(
                endpoint,
                json=request_data
            )

            logger.debug(f"Backend image generation response status: {response.status_code}")

            # Handle different response status codes
            if response.status_code == 200:
                return await self._handle_image_success_response(response)
            else:
                await self._handle_error_response(response)

        except BackendHTTPError:
            # Re-raise BackendHTTPError as-is (from _handle_error_response)
            raise

        except httpx.TimeoutException as e:
            logger.error(f"Backend timeout during image generation: {e}")
            raise BackendTimeoutError(f"Backend request timed out: {e}") from e

        except httpx.ConnectError as e:
            logger.error(f"Backend connection error during image generation: {e}")
            raise BackendConnectionError(f"Failed to connect to backend: {e}") from e

        except httpx.RequestError as e:
            logger.error(f"Backend request error during image generation: {e}")
            raise BackendConnectionError(f"Backend request failed: {e}") from e

        except Exception as e:
            logger.error(f"Unexpected error during image generation: {e}")
            raise BackendHTTPError(f"Unexpected error: {e}", 500) from e

    async def health_check(self) -> dict[str, Any]:
        """
        Perform a health check against the backend.

        Returns:
            Dict[str, Any]: Health check response

        Raises:
            BackendClientError: If health check fails
        """
        try:
            response = await self.client.get("/health")

            if response.status_code == 200:
                return response.json()
            else:
                raise BackendHTTPError(
                    f"Backend health check failed with status {response.status_code}",
                    response.status_code
                )

        except httpx.RequestError as e:
            logger.error(f"Backend health check failed: {e}")
            raise BackendConnectionError(f"Health check failed: {e}") from e

    async def _process_streaming_response(self, response: httpx.Response) -> AsyncGenerator[MedusaStreamResponse, None]:
        """
        Process a streaming response from the backend.

        Args:
            response: The streaming HTTP response

        Yields:
            MedusaStreamResponse: Parsed response chunks
        """
        buffer = ""

        async for chunk in response.aiter_text():
            buffer += chunk

            # Process complete lines
            while "\n" in buffer:
                line, buffer = buffer.split("\n", 1)
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith("#"):
                    continue

                # Handle Server-Sent Events format
                if line.startswith("data: "):
                    data_content = line[6:]  # Remove "data: " prefix

                    # Check for end of stream
                    if data_content == "[DONE]":
                        logger.debug("Received end of stream marker")
                        break

                    try:
                        # Parse JSON chunk
                        chunk_data = json.loads(data_content)

                        # Validate and yield the chunk
                        stream_chunk = MedusaStreamResponse(**chunk_data)
                        logger.debug(f"Yielding stream chunk: {stream_chunk.id}")
                        yield stream_chunk

                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse streaming chunk JSON: {e}, data: {data_content}")
                        continue
                    except Exception as e:
                        logger.warning(f"Failed to validate streaming chunk: {e}, data: {data_content}")
                        continue

    async def _handle_streaming_error_response(self, response: httpx.Response, content: bytes) -> None:
        """
        Handle an error response from a streaming request.

        Args:
            response: The HTTP response
            content: The response content

        Raises:
            BackendHTTPError: Always raises with appropriate error details
        """
        try:
            error_data = json.loads(content.decode())
            logger.warning(f"Backend streaming error response: {error_data}")

            # Try to parse as MedusaErrorResponse
            try:
                error_response = MedusaErrorResponse(**error_data)
                error_message = error_response.error.get("message", "Unknown backend streaming error")
            except Exception:
                # Fallback to raw error data
                error_message = str(error_data)

        except json.JSONDecodeError:
            error_message = f"Backend streaming returned {response.status_code}: {content.decode()}"
            error_data = {"raw_response": content.decode()}

        logger.error(f"Backend streaming HTTP error {response.status_code}: {error_message}")

        raise BackendHTTPError(
            error_message,
            response.status_code,
            error_data
        )

    async def close(self) -> None:
        """
        Close the HTTP client and clean up resources.
        """
        await self.client.aclose()
        logger.info("Backend client closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()


def get_backend_client(settings: Settings) -> BackendClientService:
    """
    Factory function to create a backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return BackendClientService(settings)
